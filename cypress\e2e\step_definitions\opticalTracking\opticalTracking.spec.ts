import { Before, Then, When } from '@badeball/cypress-cucumber-preprocessor';
import type { CypressInterceptRequest } from '../../../support/types/graphql';
import {
  DataTestSelector,
  Graphql,
  VideoResult,
  waitForJobSuccess,
} from '../../../support/helperFunction/mediaDetailHelper';
import '../common/mediaPlayerCommonStep';

Before(() => {
  cy.LoginLandingPage();
  (
    cy as Cypress.cy & {
      intercept: (
        method: string,
        url: string,
        handler: (req: CypressInterceptRequest) => void
      ) => void;
    }
  ).intercept('POST', Graphql.GraphqlURL, (req: CypressInterceptRequest) => {
    if (req.body && req.body.query === Graphql.JobQuery) {
      req.alias = 'jobStatus';
    }
  });
});

When('The user stops the video at {int} seconds', (seconds: number) => {
  cy.StopVideoAtSeconds(seconds);
});

Then('The user tracks the video', () => {
  cy.get('[data-testid="rnd-box"][type="udr"]').within(($udr) => {
    cy.wrap($udr).as('udr');
    cy.get('@udr').click({ force: true });
    cy.get('@udr').within(() => {
      cy.get('[id^="detection-icon"]').click({ force: true });
    });
    cy.wrap(Cypress.$('body'))
      .contains('li', 'Track forward only')
      .click({ force: true });
  });
  cy.getDataSetCy({ cyAlias: DataTestSelector.MovingBackground }).should(
    'be.visible'
  );
  waitForJobSuccess({
    maxRetryAttempts: 20,
  });
  cy.get('#notistack-snackbar', { timeout: 120000 }).should('be.visible');
  cy.getDataSetCy({ cyAlias: DataTestSelector.MovingBackground }).should(
    'not.exist',
    { timeout: 120000 }
  );

  cy.get('.react-draggable').should('have.length', 1);

  cy.get('.react-draggable').last().click({ force: true });
  cy.get('[id^="detection-icon"]').should('be.visible');
  cy.get(`[title="${VideoResult.OverlayGroup} 1"]`).should('be.visible');
});

Then(
  'The user draws an UDR at coordinates {int}, {int}, {int}, {int}',
  (x1: number, y1: number, x2: number, y2: number) => {
    cy.DrawAnUdr({ orderNumber: 0, x1, y1, x2, y2 });
  }
);
