const path = require('path');
const fs = require('fs/promises');
const report = require('multiple-cucumber-html-reporter');

interface CypressRunResult {
  browserName: string;
  browserVersion: string;
  osName: string;
  startedTestsAt: string;
  endedTestsAt: string;
  cypressVersion: string;
  totalFailed: number;
  totalPassed: number;
}

interface CypressConfig {
  projectRoot: string;
  projectName: string;
}

export async function generateCucumberHTMLReport(results: CypressRunResult, config: CypressConfig) {
  const cucumberReportFile = path.join(
    config.projectRoot,
    'cypress/reports',
    'cucumber-json.json'
  );
  try {
    const data = await fs.readFile(cucumberReportFile, 'utf8');
    const runInfos = JSON.parse(data);

    const runResult = results;

    report.generate({
      jsonDir: './cypress/reports',
      reportPath: 'cypress/reports/cucumber-html',
      metadata: {
        browser: {
          name: runResult.browserName,
          version: runResult.browserVersion,
        },
        device: 'Cypress',
        platform: {
          name: runResult.osName,
        },
      },
      customData: {
        title: 'Cypress Run Info',
        data: [
          { label: 'Project', value: config.projectName },
          {
            label: 'Execution Start Time',
            value: new Date(runResult.startedTestsAt).toLocaleString(),
          },
          {
            label: 'Execution End Time',
            value: new Date(runResult.endedTestsAt).toLocaleString(),
          },
          {
            label: 'Cypress Version',
            value: runResult.cypressVersion,
          },
          {
            label: 'Total Failed',
            value: runResult.totalFailed,
          },
          {
            label: 'Total Passed',
            value: runResult.totalPassed,
          },
        ],
      },
      customJson: runInfos,
    });
  } catch (err) {
    console.error('Error handling report:', err);
  }
}
