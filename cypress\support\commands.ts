import 'cypress-file-upload';
import { RedactionCodeGraphQlQuery } from './helperFunction/redactionCodeHelper';
import { ProfileGraphQlQuery } from './helperFunction/profilesPageHelper';
import { Graphql } from './helperFunction/mediaDetailHelper';
import { deleteTdoByName } from './helperFunction/landingPageHelper';
import type {
  LoginResponse,
  FetchProfilesResponse,
  DeleteProfileResponse,
  FetchRedactionCodeResponse,
  DeleteRedactionCodeResponse,
  TDODownloadResponse,
  GraphQLResponse,
  CypressResponse,
  CypressInterceptRequest,
  AssetRecord,
} from './types/graphql';

Cypress.Commands.add('LoginToApp', () => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/v1/admin/login`,
    form: true,
    body: {
      userName: Cypress.env('username'),
      password: Cypress.env('password'),
    },
  }).then((userInfo: Cypress.Response<LoginResponse>) => {
    console.log('=====', userInfo.body);
    // Cypress.env('orgId', userInfo.body.organization.organizationId);
    Cypress.env('token', userInfo.body.token);
    Cypress.env('userId', userInfo.body.userId);
    return userInfo;
  });
  cy.intercept('GET', '**/token//extend', (req: CypressInterceptRequest) => {
      req.url = req.url.replace(
        'token//extend',
        `token/${Cypress.env('token')}/extend`
      );

      req.continue();
    })
    .as('tokenExtendCall');
});

Cypress.Commands.add('LoginLandingPage', () => {
  cy.LoginToApp();
  cy.visit('/');
});

Cypress.Commands.add('DeleteUDRGroups', () => {
  cy.get('[data-test="overlay-container"]').within(() => {
    cy.get('[data-testid="rnd-box"][type="udr"]').each(($udr) => {
      cy.wrap($udr).as('udr');
      cy.get('@udr').click({ force: true });
      cy.get('@udr').within(() => {
        cy.get('[id^="detection-icon"]').click({ force: true });
      });
      cy.wrap(Cypress.$('body'))
        .contains('li', 'Delete UDR Group')
        .click({ force: true });
    });
  });
});

Cypress.Commands.add('GoToTestFile', (name: string) => {
  if (name.startsWith('fileId')) {
    cy.getKeyValue(name).then((fileId) => cy.visit(`/files/${fileId}`));
  } else {
    cy.get('[data-testId="mainpage-medialist"]').as('media');
    cy.get('@media').scrollIntoView();
    cy.get('@media').contains(name).click();
    cy.url().then((url) => {
      const id = url.match(/\/files\/(\d+)/)?.[1];
      if (id) {
        return cy.setKeyValue(`fileId_${name}`, id);
      } else {
        throw new Error(`Could not extract file ID from URL: ${url}`);
      }
    });
  }
});

Cypress.Commands.add('DeleteUDRGroups', () => {
  cy.get('[data-test="overlay-container"]').within(() => {
    cy.get('[data-testid="rnd-box"][type="udr"]').each(($udr) => {
      cy.wrap($udr).as('udr');
      cy.get('@udr').click({ force: true });
      cy.get('@udr').within(() => {
        cy.get('[id^="detection-icon"]').click({ force: true });
      });
      cy.wrap(Cypress.$('body'))
        .contains('li', 'Delete UDR Group')
        .click({ force: true });
    });
  });
});

Cypress.Commands.add('ResetAndGoToTestFile', (name: string) => {
  cy.get('[data-testId="mainpage-medialist"]').as('media');
  cy.get('@media').scrollIntoView();
  cy.get('@media')
    .contains('[data-tdo-id]', name)
    .invoke('attr', 'data-tdo-id')
    .then((tdoId) => {
      cy.log(`Resetting TDO: ${tdoId}`);
      return cy.request({
        method: 'POST',
        url: `${Cypress.env('apiRoot')}/v3/graphql`,
        body: {
          query: `query {
            temporalDataObject(id: "${tdoId}"){
              assets(limit: 200) {
                records {
                  id
                  isUserEdited
                  assetType
                }
              }
            }
          }`,
        },
      });
    })
    .then((resp: Cypress.Response<GraphQLResponse<TDODownloadResponse>>) => {
      const commands = resp.body.data.temporalDataObject.assets.records
        .filter(
          (asset: AssetRecord) =>
            asset.isUserEdited ||
            asset.assetType === 'redaction-user-selections' ||
            asset.assetType === 'redaction-media-data' ||
            asset.assetType === 'redacted-media'
        )
        .map(
          (tdo: AssetRecord, idx: number) =>
            `delete${idx}: deleteAsset(id: "${tdo.id}"){
              id
            }
            `
        );
      if (commands.length === 0) {
        return null;
      }
      return cy.request({
        method: 'POST',
        url: `${Cypress.env('apiRoot')}/v3/graphql`,
        body: {
          query: `mutation {
              ${commands.join()}
            }`,
        },
      });
    })
    .then(() => cy.GoToTestFile(name));
});

Cypress.Commands.add(
  'DrawCustomUDRsWithIncreasingCoordinates',
  (count: number) => {
    const MIN_COORD = 50;
    const MAX_COORD = 300;
    const STEP = 50;
    const usedCoordinates = new Set<string>();

    for (let i = 0; i < count; i++) {
      cy.get('#media-detail-tab').then(() => {
        let x1: number, y1: number;

        do {
          x1 = MIN_COORD + ((STEP * i) % (MAX_COORD - MIN_COORD - 50));
          y1 =
            MIN_COORD +
            ((STEP * ((i * 2 + 1) % count)) % (MAX_COORD - MIN_COORD - 50));
        } while (usedCoordinates.has(`${x1},${y1}`));

        usedCoordinates.add(`${x1},${y1}`);

        const x2: number = Math.min(x1 + 100, MAX_COORD);
        const y2: number = Math.min(y1 + 100, MAX_COORD);

        cy.DrawAnUdr({ orderNumber: i, x1, y1, x2, y2 });

        cy.get('[data-testid="results-tab-object"]').each(($el) => {
          cy.wrap($el).scrollIntoView();
          cy.wrap($el).should('be.exist');
        });

        cy.get('.react-draggable[data-testid="rnd-box"][type="udr"]').should(
          'have.length',
          i + 1
        );

        return null;
      });
    }
  }
);

Cypress.Commands.add('StopVideoAtSeconds', (seconds: number) => {
  const formattedSeconds = `0:${seconds.toString().padStart(2, '0')}`;

  cy.get('.video-react-video').should('be.exist');

  cy.get('[data-testid="media-player-container"]').within(() => {
    cy.get('[data-testid="media-player-component"]').should('be.visible');

    cy.get('.video-react-play-control.video-react-paused:visible').click();

    cy.get('.video-react-current-time-display').should(
      'contain',
      formattedSeconds
    );

    cy.get('.video-react-play-control.video-react-playing:visible').click();
  });

  cy.get('.video-react-play-control.video-react-paused').should('be.visible');
});

Cypress.Commands.add(
  'repeat',
  ({ action, times }: { action: () => void; times: number }) => {
    if (typeof action === 'function') {
      Array.from({ length: times }, () => action());
    }
  }
);
Cypress.Commands.add(
  'awaitNetworkResponseCode',
  ({
    alias,
    code,
    repeat = 1,
  }: {
    alias: string;
    code: number;
    repeat?: number;
  }) => {
    cy.repeat({
      action: () =>
        cy
          .wait(`@${alias}` as '@string')
          .its('response.statusCode')
          .should('eq', code),
      times: repeat,
    });
    cy.assertNoLoading();
  }
);

Cypress.Commands.add(
  'getDataIdCy',
  // TODO type options correctly
  ({
    idAlias,
    options = {},
  }: {
    idAlias: string;
    options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
  }) => {
    const matches = idAlias.replace(/@/, '').split(' > ');

    const tagName = matches[0];
    const childCombinators: string | string[] =
      matches.slice(1).join(' > ') ?? '';
    const withChildCombinators =
      childCombinators.length > 0 ? ` > ${childCombinators}` : '';

    return cy.get(`[data-testid="${tagName}"]${withChildCombinators}`, options);
  }
);

Cypress.Commands.add(
  'getDataSetCy',
  // TODO type options correctly
  ({
    cyAlias,
    options = {},
  }: {
    cyAlias: string;
    options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
  }) => {
    const matches = cyAlias.replace(/@/, '').split(' > ');

    const tagName = matches[0];
    const childCombinators: string | string[] =
      matches.slice(1).join(' > ') ?? '';
    const withChildCombinators =
      childCombinators.length > 0 ? ` > ${childCombinators}` : '';

    return cy.get(`[data-test="${tagName}"]${withChildCombinators}`, options);
  }
);
Cypress.Commands.add(
  'Graphql',
  <T = unknown>(query: string, variables: Record<string, unknown> = {}) => {
    cy.request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v3/graphql`,
      headers: { Authorization: 'Bearer ' + Cypress.env('token') },
      body: {
        query,
        variables,
      },
    }).then((res: Cypress.Response<T>) => cy.wrap(res));
  }
);
Cypress.Commands.add(
  'filterByAria',
  { prevSubject: true },
  (subject: string, attr: string, value: string) => {
    cy.wrap(subject).filter(`[aria-${attr}="${value}"]`);
  }
);

Cypress.Commands.add('getByRoles', function (role: string) {
  const filters =
    role.split(':').length > 1 ? ':' + role.split(':').slice(1).join(':') : '';

  if (filters === '') {
    this.role = role;
  } else {
    this.role = role.split(':')[0];
  }

  return cy.get(`[role="${this.role}"]${filters}`);
});

Cypress.Commands.add(
  'navigateToSectionByName',
  ({ sectionName }: { sectionName: string }) => {
    cy.visit(`/${sectionName}`);
  }
);

Cypress.Commands.add('deleteProfileByName', (name: string) => {
  cy.Graphql<GraphQLResponse<FetchProfilesResponse>>(
    ProfileGraphQlQuery.FetchProfiles,
    {
      schemaId: 'c36a9251-3749-4387-8924-db54a1acb124',
      limit: 1000,
      offset: 0,
    }
  ).then((res: CypressResponse<GraphQLResponse<FetchProfilesResponse>>) => {
    const records = res.body.data.structuredDataObjects.records;
    const matchingRecord = records.find(
      (record) => record.data.profileName === name
    );

    if (matchingRecord) {
      return cy
        .Graphql<GraphQLResponse<DeleteProfileResponse>>(
          ProfileGraphQlQuery.DeleteProfile,
          {
            profileId: matchingRecord.id,
            schemaId: 'c36a9251-3749-4387-8924-db54a1acb124',
          }
        )
        .then(
          (res2: CypressResponse<GraphQLResponse<DeleteProfileResponse>>) => {
            const deletedItem = res2.body.data.deleteStructuredData;
            expect(deletedItem.id).to.equal(matchingRecord.id);
            cy.log('Deleted record:', matchingRecord);
            return res2;
          }
        );
    } else {
      cy.log('Record not found with profileName:', name);
      return cy.wrap(null);
    }
  });
});

Cypress.Commands.add(
  'interceptGraphQLQuery',
  (query: string, alias: string) => {
    cy.intercept('POST', /v3\/graphql/, (req: CypressInterceptRequest) => {
        if (
          req.body &&
          typeof req.body === 'object' &&
          'query' in req.body &&
          req.body.query === query
        ) {
          req.alias = alias;
        }
      });
  }
);

Cypress.Commands.add(
  'toHaveCssProperty',
  { prevSubject: true },
  (subject: JQuery<HTMLElement>, attr: string, value?: string) => {
    cy.wrap(subject).should('have.css', attr, value);
  }
);

Cypress.Commands.add(
  'deleteRedactionCodeIfExist',
  (redactionCodeName: string) => {
    cy.Graphql<GraphQLResponse<FetchRedactionCodeResponse>>(
      RedactionCodeGraphQlQuery.FetchRedactionCode,
      {
        schemaId: '1946dcfc-a589-4329-9bd3-abfed5585f58',
        limit: 1000,
        offset: 0,
      }
    ).then(
      (res: CypressResponse<GraphQLResponse<FetchRedactionCodeResponse>>) => {
        const records = res.body.data.structuredDataObjects.records;
        const matchingRecord = records.find(
          (record) => record.data.codeName === redactionCodeName
        );
        if (matchingRecord) {
          return cy
            .Graphql<GraphQLResponse<DeleteRedactionCodeResponse>>(
              RedactionCodeGraphQlQuery.DeleteRedactionCode,
              {
                schemaId: '1946dcfc-a589-4329-9bd3-abfed5585f58',
                sdoId: matchingRecord.id,
              }
            )
            .then(
              (
                res2: CypressResponse<
                  GraphQLResponse<DeleteRedactionCodeResponse>
                >
              ) => {
                const deletedItem = res2.body.data.deleteStructuredData;
                expect(deletedItem.id).to.equal(matchingRecord.id);
                cy.log('Deleted record:', matchingRecord);
                // TODO: Remove this cy.reload() once the UI automatically reflects the deletion
                cy.reload();
                return res2;
              }
            );
        } else {
          return cy.log(
            'Record not found with Redaction Code name:',
            redactionCodeName
          );
        }
      }
    );
  }
);



Cypress.Commands.add(
  'toNotHaveCssProperty',
  { prevSubject: true },
  (subject: JQuery<HTMLElement>, attr: string, value?: string) => {
    cy.wrap(subject).should('not.have.css', attr, value);
  }
);

Cypress.Commands.add(
  'setKeyValue',
  (keyName: string, value: string | number) => {
    cy.task('setValue', { key: keyName, value: value });
  }
);

Cypress.Commands.add('getKeyValue', (keyName: string) => {
  cy.task('getValue', { key: keyName });
});
Cypress.Commands.add('getState', () => {
  cy.window().its('store').invoke('getState');
});

Cypress.Commands.add('deleteAllRedacted', (tdoId: string) => {
  cy.Graphql<GraphQLResponse<TDODownloadResponse>>(Graphql.ListAssetsQuery, {
    tdoId,
  })
    .then((response: CypressResponse<GraphQLResponse<TDODownloadResponse>>) => {
      const assets = response.body.data.temporalDataObject.assets.records;
      if (assets.length === 0) {
        return cy.log('No assets to delete');
      }

      const redactionAssets = assets.filter(
        (asset) =>
          asset.assetType === 'redaction-user-selections' ||
          asset.assetType === 'redaction-media-data' ||
          asset.assetType === 'redacted-media'
      );

      const deleteCommands = redactionAssets.map(
        (asset, idx: number) =>
          `delete${idx}: deleteAsset(id: "${asset.id}") {
          id
        }`
      );

      const deleteQuery = `
      mutation {
        ${deleteCommands.join('\n')}
      }`;

      return cy.Graphql(deleteQuery);
    })
    .then(() => {
      cy.reload();
      return cy
        .get('[data-test="overlay-container"]', { timeout: 120000 })
        .should('exist');
    });
});

Cypress.Commands.add('clearTrimRange', (tdoId: string) => {
  cy.Graphql<GraphQLResponse<TDODetailResponse>>(Graphql.getTDODetails, {
    tdoId,
  })
    .then((response: CypressResponse<GraphQLResponse<TDODetailResponse>>) => {
      const currentDetails = response.body.data.temporalDataObject.details;
      const updatedDetails = { ...currentDetails, redact: { tasks: [] } };
      return cy.Graphql<GraphQLResponse<UpdateSettingsTDOResponse>>(
        Graphql.updateTDO,
        {
          tdoId,
          details: updatedDetails,
        }
      );
    })
    .then(
      (
        updateResponse: CypressResponse<
          GraphQLResponse<UpdateSettingsTDOResponse>
        >
      ) => {
        expect(updateResponse.body.data.updateTDO.id).to.equal(tdoId);
        return cy.wrap(updateResponse);
      }
    );
});

Cypress.Commands.add('assertNoLoading', () => {
  Cypress.config('defaultCommandTimeout', 30000);
  cy.getByRoles('progressbar').should('not.exist');
});

Cypress.Commands.add('deleteTdoByName', (nameList: string[]) => {
  deleteTdoByName(nameList);
});
