import { envData } from '../fixtures/envData';
interface EnvironmentConfig {
  ENVIRONMENT?: string;
  apiRoot?: string;
  glcApiRoot?: string;
}

interface InputConfig {
  env: EnvironmentConfig;
  baseUrl: string;
}

export const addEnvToConfig = (
  _on: Cypress.PluginEvents,
  config: InputConfig
) => {
  const env = config.env.ENVIRONMENT;
  if (env && envData[env as keyof typeof envData]) {
    config.baseUrl = envData[env as keyof typeof envData].baseUrl;
    config.env.apiRoot = envData[env as keyof typeof envData].apiRoot;
    config.env.glcApiRoot = envData[env as keyof typeof envData].glcApiRoot;
  } else {
    if (!config.env.apiRoot) {
      // use config.json
      config.env.apiRoot = 'https://api.stage.us-gov-2.veritone.com';
      config.env.glcApiRoot = 'https://glc-backend.stage.us-gov-2.veritone.com';
    }
  }
  return config;
};
